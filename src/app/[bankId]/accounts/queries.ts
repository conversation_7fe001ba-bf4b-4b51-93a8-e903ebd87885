import { defineQueries, defineQuery } from '@/lib/state/defineQuery'
import { withMetrics } from '@/lib/middleware/withMetrics'
import { revenueConnectClient } from '@/api/revenueConnectClient'

/**
 * Get account
 */
const getAccount = defineQuery(
  revenueConnectClient,
  '/getAccount',
  (request) => [
    request.applicationId,
    request.accountNumber,
    request.bankNumber,
    request.effectiveDate,
  ],
)

/**
 * Get accounts
 */
const getAccounts = defineQuery(
  revenueConnectClient,
  '/getAccounts',
  (request) => [request.effectiveDate],
)

/**
 * Get account timeline
 */
const getAccountTimeline = defineQuery(
  revenueConnectClient,
  '/getAccountTimeline',
  (request) => [request.applicationId, request.accountNumber],
)

/**
 * Get account mappings
 */
const getAccountMappings = defineQuery(
  revenueConnectClient,
  '/getAccountMappings',
  (request) => [request.code, request.effectiveDate],
)

const getLeadCompositeAccountCode = defineQuery(
  revenueConnectClient,
  '/getLeadCompositeAccountCode',
  (request) => [request.code, request.effectiveDate],
)

/**
 * Get key account mappings
 *
 * QUICK AND DIRTY HACK after https://bitbucket.fis.dev/projects/AFIN/repos/af_fpb/pull-requests/264/overview
 * removed keyAccountCode from Account. Might be cleaned up with https://jira.fis.dev/browse/REVCON-53.
 */
const listKeyAccountMappings = defineQuery(
  revenueConnectClient,
  '/listAllKeyAccountMappings',
)

/*
 * Get next available account number
 */
const getNextAvailableAccountNumber = defineQuery(
  revenueConnectClient,
  '/getNextAvailableAccountNumber',
)

/**
 * Get parentless open accounts
 */
const getParentlessOpenAccounts = defineQuery(
  revenueConnectClient,
  '/getParentlessOpenAccounts',
  (request) => [request.effectiveDate],
)

/**
 * Get user fields selections
 */
const getUserFieldSelections = defineQuery(
  revenueConnectClient,
  '/getUserFieldSelections',
  (request) => [
    request.applicationId,
    request.bankNumber,
    request.accountNumber,
    request.effectiveDate,
  ],
)

/**
 * Get addresses for account
 */
export const getAddresses = defineQuery(
  revenueConnectClient,
  '/getAddresses',
  (request) => [
    request.applicationId,
    request.accountNumber,
    request.effectiveDate,
  ],
  {
    enabled: (query) => !!query.queryKey.at(1),
  },
)

/**
 * Statement Package queries
 */
export const listStatementPackages = defineQuery(
  revenueConnectClient,
  '/listStatementPackages',
  (request) => [
    request.applicationId,
    request.bankNumber,
    request.effectiveDate,
    request.accountNumber,
  ],
)

/**
 * Get service overrides for account
 */
export const getServiceOverridesByAccount = defineQuery(
  revenueConnectClient,
  '/getServiceOverridesByAccount',
  (request) => [request.code, request.effectiveDate],
)

/**
 * Get Settlement Account Info
 */

export const getAccountTypeOverride = defineQuery(
  revenueConnectClient,
  '/getAccountTypeOverride',
  (request) => [
    request.accountNumber,
    request.applicationId,
    request.bankNumber,
    request.effectiveDate,
  ],
)

/**
 * All accounts queries
 */
export const accountQueries = defineQueries(
  [
    getAccount,
    getAccounts,
    getAccountTimeline,
    getAccountMappings,
    getAccountTypeOverride,
    listKeyAccountMappings,
    getNextAvailableAccountNumber,
    getParentlessOpenAccounts,
    getUserFieldSelections,
    getAddresses,
    getLeadCompositeAccountCode,
    getServiceOverridesByAccount,
    listStatementPackages,
    // Note: getSettlementProcessingOptions is a custom function, not part of accountQueries
  ],
  withMetrics(),
)
