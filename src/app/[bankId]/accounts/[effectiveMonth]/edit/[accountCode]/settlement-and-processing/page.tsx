'use client'

import {
  InfoSectionDescription,
  InfoSectionTitle,
} from '@/components/InfoSection'
import { EditAccountContent } from '../_components/EditAccountContent'
import { useRoute } from '../routing'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'

import { UserFieldListItem } from '@/app/[bankId]/accounts/types'
import {
  AccountTypeOverrideForm,
  formToApiSchemas,
  formValidators,
} from '@/api/formToApiSchema'
import { useForm } from '@tanstack/react-form'
import { useMutation } from '@tanstack/react-query'
import { accountMutation } from '@/app/[bankId]/accounts/mutations'
import { useRouter } from 'next/navigation'
import { routeTo as routeToViewAccount } from '../../../view/[accountCode]/routing'

import {
  accountToAccountCode,
  getUserFieldsListItemsFromConfigsAndSelections,
  accountCodeToString,
} from '@/app/[bankId]/accounts/accountHelpers'
import { useMemo } from 'react'
import { useUserFieldsRelatedEntities } from '@/app/[bankId]/accounts/_hooks/useUserFieldsRelatedEntities'
import { EditAccountFooter } from '../_components/EditAccountFooter'

import EditSettlementAndProcessingOptions from './EditSettlementAndProcessingOptions'
import EditSettlementOptions from './EditSettlementOptions'

export default function Page() {
  const router = useRouter()
  const route = useRoute()!
  const routeParams = data(route).params
  const accountFromParams = useAccountFromParams(routeParams)
  const account = accountFromParams.account!
  const accountCode = accountToAccountCode(account)
  const effectiveDate = accountFromParams.effectiveDate

  const { mutate: updateSettlementAndProcessingOptions } = useMutation(
    accountMutation('/updateSettlementProcessingOptions', {
      onSuccess: () => {
        router.push(
          routeToViewAccount(
            '/accounts/[effectiveMonth]/view/[accountCode]/settlement-and-processing',
            routeParams,
          ),
        )
      },
    }),
  )

  const { userFieldsConfigurations, userFieldSelections } =
    useUserFieldsRelatedEntities({
      effectiveDate,
      applicationId: account.applicationId,
      accountNumber: account.accountNumber,
      bankNumber: account.bankNumber,
    })

  const userFields = useMemo<UserFieldListItem[]>(() => {
    return getUserFieldsListItemsFromConfigsAndSelections(
      accountCode,
      userFieldsConfigurations.data,
      userFieldSelections.data,
    ).map((originalUserField) => {
      const userField = { ...originalUserField }
      userField.selection.effectiveDate = effectiveDate
      return userField
    })
  }, [userFieldsConfigurations.data, userFieldSelections.data, effectiveDate])

  const form = useForm<AccountTypeOverrideForm>({
    defaultValues: {
      accountCode: accountToAccountCode(account),
      effectiveDate: accountFromParams.effectiveDate,
      isOverrideAsSettlementAccount: true,
      chargeAccountCode:
        account.keyAccountCode ?
          accountCodeToString(account.keyAccountCode)
        : accountCodeToString(accountToAccountCode(account)),
      analysisResultOptionsPlanCode: null,
      balanceRequirementDefinitionCode: null,
      earningsCreditDefinitionCode: null,
      interestRequirementDefinitionCode: null,
      investableBalanceDefinitionCode: null,
      reserveRequirementDefinitionCode: null,
      settlementCyclePlanCode: null,
      statementCyclePlanCode: null,
      statementFormatPlanCode: null,
      statementMessagePlanCode: null,
      analysisResultOptionsPlanCodeExpiry: null,
      balanceRequirementDefinitionCodeExpiry: null,
      earningsCreditDefinitionCodeExpiry: null,
      interestRequirementDefinitionCodeExpiry: null,
      investableBalanceDefinitionCodeExpiry: null,
      reserveRequirementDefinitionCodeExpiry: null,
      settlementCyclePlanCodeExpiry: null,
      statementCyclePlanCodeExpiry: null,
      statementFormatPlanCodeExpiry: null,
      statementMessagePlanCodeExpiry: null,
    },
    validators: {
      onChange: formValidators.accountTypeOverride,
    },
    onSubmit: ({ value }) => {
      updateSettlementAndProcessingOptions(
        formToApiSchemas.accountTypeOverride.parse({
          ...value,
          accountTypeOverride: account,
        }),
      )
    },
  })

  return (
    <form
      className='flex h-full flex-col'
      onSubmit={(event) => {
        event.preventDefault()
        event.stopPropagation()
        form.handleSubmit()
      }}
    >
      <EditAccountContent>
        <InfoSectionTitle>Configure Settlement and Processing</InfoSectionTitle>
        <InfoSectionDescription className='-mt-3'>
          {`You can override the following settings for your composite account ${account.applicationId === 'C' ? 'composite' : 'deposit'} account.`}
        </InfoSectionDescription>
        <EditSettlementAndProcessingOptions
          form={form}
          effectiveDate={effectiveDate}
          account={account}
        />
        <EditSettlementOptions form={form} effectiveDate={effectiveDate} />
      </EditAccountContent>

      <EditAccountFooter form={form} />
    </form>
  )
}
