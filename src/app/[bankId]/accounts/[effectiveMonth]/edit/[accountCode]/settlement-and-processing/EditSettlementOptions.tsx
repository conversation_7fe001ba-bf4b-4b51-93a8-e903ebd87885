import { Account, AccountTypeOverrideForm } from '@/api/formToApiSchema'
import { ReactFormExtendedApi } from '@tanstack/react-form'
import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { SortedTable } from '@/components/Table/SortedTable'
import {
  InfoSectionTitle,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { Toggle, ToggleSwitch } from '@/components/Toggle'
import { Select } from '@/components/Input/Select'
import { Checkbox } from '@/components/Checkbox'
import { MonthPicker } from '@/components/Filter/MonthPicker'
import { parseServerFormat } from '@/lib/date'

interface EditSettlementOptionsProps {
  form: ReactFormExtendedApi<AccountTypeOverrideForm>
  effectiveDate: string
}

type SettlementOptionItemWithId = {
  id: string
  name: string
  field: string
  override: boolean
  plan: string
  expiry: string | null
}

export default function EditSettlementOptions({
  form,
  effectiveDate,
}: EditSettlementOptionsProps) {
  // Sample data for settlement options - replace with API/configuration
  const settlementOptionsData: SettlementOptionItemWithId[] = [
    {
      id: '1',
      name: 'Settlement Cycle Plan',
      field: 'settlementCyclePlanCode',
      override: false,
      plan: '',
      expiry: null,
    },
    {
      id: '2',
      name: 'Statement Cycle Plan',
      field: 'statementCyclePlanCode',
      override: false,
      plan: '',
      expiry: null,
    },
    {
      id: '3',
      name: 'Statement Format Plan',
      field: 'statementFormatPlanCode',
      override: false,
      plan: '',
      expiry: null,
    },
  ]

  const columns = useMemo<ColumnDef<SettlementOptionItemWithId>[]>(() => {
    return [
      {
        header: 'Override',
        meta: {
          className: 'w-30 px-6 py-4',
        },
        cell: ({ row }: { row: { original: SettlementOptionItemWithId } }) => {
          return (
            <div>
              <Toggle defaultChecked={row.original.override}>
                <ToggleSwitch
                  label={`Override ${row.original.name}`}
                  onChange={(checked) => {
                    console.log(`Override ${row.original.field}:`, checked)
                  }}
                />
              </Toggle>
            </div>
          )
        },
      },
      {
        header: 'Settlement setting',
        accessorKey: 'name',
        cell: ({ row }: { row: { original: SettlementOptionItemWithId } }) => {
          return <div className='ml-3 basis-full'>{row.original.name}</div>
        },
      },
      {
        header: 'Plan',
        cell: ({ row }: { row: { original: SettlementOptionItemWithId } }) => {
          const planOptions = ['plan1', 'plan2', 'plan3']
          const planLabels: Record<string, string> = {
            plan1: 'Plan 1',
            plan2: 'Plan 2',
            plan3: 'Plan 3',
          }

          return (
            <div className='basis-full'>
              <Select
                className='mt-6'
                name={`plan-${row.original.id}`}
                value={row.original.plan}
                onChange={(value) => {
                  console.log(`Plan for ${row.original.field}:`, value)
                }}
                options={planOptions}
                renderOption={(option) => planLabels[option] || option}
                renderSelected={(option) => planLabels[option] || option}
                placeholder='Select a plan'
              />
            </div>
          )
        },
      },
      {
        header: 'Expiration date',
        cell: ({ row }: { row: { original: SettlementOptionItemWithId } }) => {
          const expiry = row.original.expiry

          const handleCheckboxChange = (checked: boolean) => {
            console.log(`No expiration for ${row.original.field}:`, checked)
          }

          const handleDateChange = (date: string) => {
            console.log(`Expiration date for ${row.original.field}:`, date)
          }

          return (
            <div className='flex flex-row gap-3'>
              <Checkbox
                checked={!expiry}
                label='No expiration'
                onChange={handleCheckboxChange}
              />
              {expiry && (
                <MonthPicker
                  className='my-1 h-9 min-w-32 bg-white ring-1 ring-inset ring-gray-300'
                  initialDate={parseServerFormat(expiry)}
                  onDateChange={handleDateChange}
                />
              )}
            </div>
          )
        },
      },
    ]
  }, [])

  return (
    <div className='mt-4 flex flex-col gap-6'>
      <InfoSectionTitle>Settlement Options</InfoSectionTitle>
      <InfoSectionDescription className='-mt-3'>
        These Settings are inherited from account type : {}.
      </InfoSectionDescription>

      <SortedTable
        data={settlementOptionsData}
        columns={columns}
        columnFilters={[]}
      />
    </div>
  )
}
