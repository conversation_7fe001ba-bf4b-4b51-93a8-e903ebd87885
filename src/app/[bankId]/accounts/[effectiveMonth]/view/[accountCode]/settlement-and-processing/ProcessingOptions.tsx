'use client'

import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { SortedTable } from '@/components/Table/SortedTable'
import {
  InfoSectionTitle,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { ProcessingOptionItem } from './types'

interface ProcessingOptionsProps {
  processingOptions: ProcessingOptionItem[]
}

export default function ProcessingOptions({
  processingOptions,
}: ProcessingOptionsProps) {
  const processingOptionsData = processingOptions

  const columns = useMemo<ColumnDef<ProcessingOptionItem>[]>(() => {
    return [
      {
        header: 'Override',
        accessorKey: 'override',
        meta: {
          className: 'basis-20',
        },
        cell: ({ row }) => (
          <div className='flex justify-center'>
            {row.original.override ? (
              <svg
                className="h-4 w-4 text-black"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            ) : (
              null
            )}
          </div>
        ),
      },
      {
        header: 'Processing setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        accessorKey: 'plan',
        meta: {
          className: 'basis-full',
        },
      },
      {
        header: 'Expiration date',
        accessorKey: 'expiry',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => row.original.expiry || 'No expiration',
      },
    ]
  }, [])

  return (
    <div className='space-y-4'>
      <div>
        <InfoSectionTitle>Processing options</InfoSectionTitle>
        <InfoSectionDescription>
          Configure processing-specific options for this account.
        </InfoSectionDescription>
      </div>
      <SortedTable
        data={processingOptionsData}
        columns={columns}
      />
    </div>
  )
}
