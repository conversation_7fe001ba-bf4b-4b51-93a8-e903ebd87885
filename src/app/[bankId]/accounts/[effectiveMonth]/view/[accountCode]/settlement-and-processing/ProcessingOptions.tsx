'use client'

import { useMemo } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { SortedTable } from '@/components/Table/SortedTable'
import {
  InfoSectionTitle,
  InfoSectionDescription,
} from '@/components/InfoSection'
import { ProcessingOptionItem } from './types'

interface ProcessingOptionsProps {
  processingOptions: ProcessingOptionItem[]
}

export default function ProcessingOptions({
  processingOptions,
}: ProcessingOptionsProps) {
  const processingOptionsData = processingOptions

  const columns = useMemo<ColumnDef<ProcessingOptionItem>[]>(() => {
    return [
      {
        header: 'Override',
        accessorKey: 'override',
        meta: {
          className: 'basis-20',
        },
        cell: ({ row }) => (
          <div className='flex justify-center'>
            {row.original.override ? (
              <div className='h-4 w-4 rounded-full bg-green-500'></div>
            ) : (
              <div className='h-4 w-4 rounded-full border-2 border-gray-300'></div>
            )}
          </div>
        ),
      },
      {
        header: 'Processing setting',
        accessorKey: 'name',
        meta: {
          className: 'ml-4 basis-full',
        },
      },
      {
        header: 'Plan',
        accessorKey: 'plan',
        meta: {
          className: 'basis-full',
        },
      },
      {
        header: 'Expiration date',
        accessorKey: 'expiry',
        meta: {
          className: 'basis-full',
        },
        cell: ({ row }) => row.original.expiry || 'No expiration',
      },
    ]
  }, [])

  return (
    <div className='space-y-4'>
      <div>
        <InfoSectionTitle>Processing options</InfoSectionTitle>
        <InfoSectionDescription>
          Configure processing-specific options for this account.
        </InfoSectionDescription>
      </div>
      <SortedTable
        data={processingOptionsData}
        columns={columns}
      />
    </div>
  )
}
