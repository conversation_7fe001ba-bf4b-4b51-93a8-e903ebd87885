import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { z } from 'zod'

// Reuse existing types from apiToFormSchema
export type AccountCode = z.infer<typeof apiToFormSchemas.accountCode>
export type AccountTypeOverride = z.infer<typeof apiToFormSchemas.accountTypeOverride>

// API Response type based on the provided structure
export interface GetSettlementProcessingOptionsResponse {
  accountCode: AccountCode
  effectiveDate: string
  overrideAsSettlementAccount: boolean
  accountTypeOverride: AccountTypeOverride
}

// Settlement option item for display
export interface SettlementOptionItem {
  id: string
  name: string
  field: string
  override: boolean
  plan: string
  planCode?: string
  expiry: string | null
}

// Processing option item for display
export interface ProcessingOptionItem {
  id: string
  name: string
  field: string
  override: boolean
  plan: string
  planCode?: string
  expiry: string | null
}

// Transformed data for UI components
export interface SettlementProcessingOptionsData {
  settlementOptions: SettlementOptionItem[]
  processingOptions: ProcessingOptionItem[]
}
