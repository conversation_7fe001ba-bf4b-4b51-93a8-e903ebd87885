import { apiToFormSchemas } from '@/api/apiToFormSchema'
import { z } from 'zod'

// Reuse existing types from apiToFormSchema
export type AccountCode = z.infer<typeof apiToFormSchemas.accountCode>
export type AccountTypeOverride = z.infer<typeof apiToFormSchemas.accountTypeOverride>

// API Response type based on the provided structure
export interface GetSettlementProcessingOptionsResponse {
  accountCode: AccountCode
  effectiveDate: string
  overrideAsSettlementAccount: boolean
  accountTypeOverride: AccountTypeOverride
}

// Settlement option item for display
export interface SettlementOptionItem {
  id: string
  name: string
  field: string
  override: boolean
  plan: string
  planCode?: string
  expiry: string | null
}

// Processing option item for display
export interface ProcessingOptionItem {
  id: string
  name: string
  field: string
  override: boolean
  plan: string
  planCode?: string
  expiry: string | null
}

// Transformed data for UI components
export interface SettlementProcessingOptionsData {
  settlementOptions: SettlementOptionItem[]
  processingOptions: ProcessingOptionItem[]
}

// Transform API response to UI data
export const transformSettlementProcessingOptions = (
  apiResponse: GetSettlementProcessingOptionsResponse,
): SettlementProcessingOptionsData => {
  const { accountTypeOverride } = apiResponse

  const settlementOptions: SettlementOptionItem[] = [
    {
      id: '1',
      name: 'Analysis result options',
      field: 'analysisResultOptionsPlanCode',
      override: !!accountTypeOverride?.analysisResultOptionsPlanCode,
      plan: accountTypeOverride?.analysisResultOptionsPlanCode || 'Default',
      planCode: accountTypeOverride?.analysisResultOptionsPlanCode || undefined,
      expiry: accountTypeOverride?.analysisResultOptionsPlanCodeExpiry || null,
    },
    {
      id: '2',
      name: 'Settlement cycle',
      field: 'settlementCyclePlanCode',
      override: !!accountTypeOverride?.settlementCyclePlanCode,
      plan: accountTypeOverride?.settlementCyclePlanCode || 'Default',
      planCode: accountTypeOverride?.settlementCyclePlanCode || undefined,
      expiry: accountTypeOverride?.settlementCyclePlanCodeExpiry || null,
    },
  ]

  const processingOptions: ProcessingOptionItem[] = [
    {
      id: '1',
      name: 'Earnings credit',
      field: 'earningsCreditDefinitionCode',
      override: !!accountTypeOverride?.earningsCreditDefinitionCode,
      plan: accountTypeOverride?.earningsCreditDefinitionCode || 'Default',
      planCode: accountTypeOverride?.earningsCreditDefinitionCode || undefined,
      expiry: accountTypeOverride?.earningsCreditDefinitionCodeExpiry || null,
    },
    {
      id: '2',
      name: 'Investable balance',
      field: 'investableBalanceDefinitionCode',
      override: !!accountTypeOverride?.investableBalanceDefinitionCode,
      plan: accountTypeOverride?.investableBalanceDefinitionCode || 'Default',
      planCode: accountTypeOverride?.investableBalanceDefinitionCode || undefined,
      expiry: accountTypeOverride?.investableBalanceDefinitionCodeExpiry || null,
    },
    {
      id: '3',
      name: 'Balance requirement',
      field: 'balanceRequirementDefinitionCode',
      override: !!accountTypeOverride?.balanceRequirementDefinitionCode,
      plan: accountTypeOverride?.balanceRequirementDefinitionCode || 'Default',
      planCode: accountTypeOverride?.balanceRequirementDefinitionCode || undefined,
      expiry: accountTypeOverride?.balanceRequirementDefinitionCodeExpiry || null,
    },
    {
      id: '4',
      name: 'Statement cycle',
      field: 'statementCyclePlanCode',
      override: !!accountTypeOverride?.statementCyclePlanCode,
      plan: accountTypeOverride?.statementCyclePlanCode || 'Default',
      planCode: accountTypeOverride?.statementCyclePlanCode || undefined,
      expiry: accountTypeOverride?.statementCyclePlanCodeExpiry || null,
    },
    {
      id: '5',
      name: 'Statement format',
      field: 'statementFormatPlanCode',
      override: !!accountTypeOverride?.statementFormatPlanCode,
      plan: accountTypeOverride?.statementFormatPlanCode || 'Default',
      planCode: accountTypeOverride?.statementFormatPlanCode || undefined,
      expiry: accountTypeOverride?.statementFormatPlanCodeExpiry || null,
    },
    {
      id: '6',
      name: 'Interest',
      field: 'interestRequirementDefinitionCode',
      override: !!accountTypeOverride?.interestRequirementDefinitionCode,
      plan: accountTypeOverride?.interestRequirementDefinitionCode || 'Default',
      planCode: accountTypeOverride?.interestRequirementDefinitionCode || undefined,
      expiry: accountTypeOverride?.interestRequirementDefinitionCodeExpiry || null,
    },
  ]

  return {
    settlementOptions,
    processingOptions,
  }
}

// Mock data generator for development
export const generateMockSettlementProcessingOptions = (): SettlementProcessingOptionsData => {
  return {
    settlementOptions: [
      {
        id: '1',
        name: 'Analysis result options',
        field: 'analysisResultOptionsPlanCode',
        override: true,
        plan: 'Pay Fees & Interest Monthly - 100',
        planCode: '100',
        expiry: null,
      },
      {
        id: '2',
        name: 'Settlement cycle',
        field: 'settlementCyclePlanCode',
        override: true,
        plan: 'Default - Monthly - 99999',
        planCode: '99999',
        expiry: null,
      },
    ],
    processingOptions: [
      {
        id: '1',
        name: 'Earnings credit',
        field: 'earningsCreditDefinitionCode',
        override: true,
        plan: 'Commercial- Tiered - 250',
        planCode: '250',
        expiry: '2025-08',
      },
      {
        id: '2',
        name: 'Investable balance',
        field: 'investableBalanceDefinitionCode',
        override: false,
        plan: '+col - Reserves - 999999',
        planCode: '999999',
        expiry: null,
      },
      {
        id: '3',
        name: 'Balance requirement',
        field: 'balanceRequirementDefinitionCode',
        override: false,
        plan: '10% Reserves - 99999',
        planCode: '99999',
        expiry: null,
      },
      {
        id: '4',
        name: 'Statement cycle',
        field: 'statementCyclePlanCode',
        override: false,
        plan: 'Default - 99999',
        planCode: '99999',
        expiry: null,
      },
      {
        id: '5',
        name: 'Statement format',
        field: 'statementFormatPlanCode',
        override: false,
        plan: 'Default - Monthly - 99999',
        planCode: '99999',
        expiry: null,
      },
      {
        id: '6',
        name: 'Interest',
        field: 'interestRequirementDefinitionCode',
        override: false,
        plan: 'No plan assigned - 0',
        planCode: '0',
        expiry: null,
      },
    ],
  }
}
