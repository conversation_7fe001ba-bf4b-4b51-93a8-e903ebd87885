'use client'

import {
  InfoSection,
  InfoSectionSeparator,
  InfoSectionTitle,
} from '@/components/InfoSection'
import Link from 'next/link'
import { Button } from '@/components/Button'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRoute } from '../routing'
import { routeTo } from '../../../edit/[accountCode]/routing'
import { ViewSettlementAccount } from './ViewSettlementAccount'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { useQuery } from '@tanstack/react-query' 
import { toUTCDateString } from '@/lib/date'
import SettlementOptions from './SettlementOptions'
import ProcessingOptions from './ProcessingOptions'
import {
  transformSettlementProcessingOptions,
  generateMockSettlementProcessingOptions,
  GetSettlementProcessingOptionsResponse
} from './types'
import { accountToAccountCode } from '@/app/[bankId]/accounts/accountHelpers'
import { toUIFormat } from '@/lib/date'

export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const [applicationId, bankNumber, accountNumber] =
    routeParams.accountCode.split('-') as ['C' | 'D', string, string]
  const { account } = useAccountFromParams(routeParams)

  const {
    data: accountTypeOverride,
    isLoading,
    isError,
  } = useQuery(
    accountQueries('/getAccountTypeOverride', {
      bankNumber,
      applicationId,
      accountNumber,
      effectiveDate: toUTCDateString(routeParams.effectiveMonth),
    }),
  )
  if (!account) {
    return <>404 could not load account.</>
  }

  if (isLoading) {
    return <div>Loading settlement and processing options...</div>
  }

  return (
    <div className='flex justify-between'>
      <InfoSection className='w-full !gap-1 md:w-3/4'>
        <div className='flex items-center justify-between'>
          <InfoSectionTitle>Settlements And Processing</InfoSectionTitle>
          <div className='flex gap-2'>
            <Button className='btn'>Export</Button>
            <Link
              href={`${routeTo(
                '/accounts/[effectiveMonth]/edit/[accountCode]/settlement-and-processing',
                routeParams,
              )}`}
            >
              <Button className='btn-primary text-white'>Edit</Button>
            </Link>
          </div>
        </div>
        <p className='text-zinc-400'>
          You can override the following settings for your composite account.
        </p>
        <ViewSettlementAccount
          account={account}
          chargeAccountCode={accountTypeOverride?.chargeAccountCode}
        />
        <InfoSectionSeparator className='mb-3 mt-3' />
        <SettlementOptions settlementOptions={}/>
        <InfoSectionSeparator className='mb-3 mt-3' />
        <ProcessingOptions settlementOptions={}/>
      </InfoSection>
    </div>
  )
}
