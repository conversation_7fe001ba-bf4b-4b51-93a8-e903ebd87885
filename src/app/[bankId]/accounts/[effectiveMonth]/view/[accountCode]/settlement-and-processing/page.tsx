'use client'

import {
  InfoSection,
  InfoSectionSeparator,
  InfoSectionTitle,
} from '@/components/InfoSection'
import Link from 'next/link'
import { Button } from '@/components/Button'
import { data } from '@/lib/unions/Union'
import { useAccountFromParams } from '@/app/[bankId]/accounts/_hooks/useAccountFromParams'
import { useRoute } from '../routing'
import { routeTo } from '../../../edit/[accountCode]/routing'
import { ViewSettlementAccount } from './ViewSettlementAccount'
import { accountQueries } from '@/app/[bankId]/accounts/queries'
import { useQuery } from '@tanstack/react-query' 
import { toUTCDateString } from '@/lib/date'
import SettlementOptions from './SettlementOptions'
import ProcessingOptions from './ProcessingOptions'
import {
  SettlementOptionItem,
  ProcessingOptionItem,
} from './types'


export default function Page() {
  const route = useRoute()!
  const routeParams = data(route).params
  const [applicationId, bankNumber, accountNumber] =
    routeParams.accountCode.split('-') as ['C' | 'D', string, string]
  const { account } = useAccountFromParams(routeParams)

  const {
    data: accountTypeOverride,
    isLoading,
    isError,
  } = useQuery(
    accountQueries('/getAccountTypeOverride', {
      bankNumber,
      applicationId,
      accountNumber,
      effectiveDate: toUTCDateString(routeParams.effectiveMonth),
    }),
  )

  // Transform the API response to UI data
  const settlementOptions: SettlementOptionItem[] = accountTypeOverride ? [
    {
      id: '1',
      name: 'Analysis result options',
      field: 'analysisResultOptionsPlanCode',
      override: !!accountTypeOverride.analysisResultOptionsPlanCode,
      plan: accountTypeOverride.analysisResultOptionsPlanCode || 'Default',
      planCode: accountTypeOverride.analysisResultOptionsPlanCode || undefined,
      expiry: accountTypeOverride.analysisResultOptionsPlanCodeExpiry || null,
    },
    {
      id: '2',
      name: 'Settlement cycle',
      field: 'settlementCyclePlanCode',
      override: !!accountTypeOverride.settlementCyclePlanCode,
      plan: accountTypeOverride.settlementCyclePlanCode || 'Default',
      planCode: accountTypeOverride.settlementCyclePlanCode || undefined,
      expiry: accountTypeOverride.settlementCyclePlanCodeExpiry || null,
    },
  ] : []

  const processingOptions: ProcessingOptionItem[] = accountTypeOverride ? [
    {
      id: '1',
      name: 'Earnings credit',
      field: 'earningsCreditDefinitionCode',
      override: !!accountTypeOverride.earningsCreditDefinitionCode,
      plan: accountTypeOverride.earningsCreditDefinitionCode || 'Default',
      planCode: accountTypeOverride.earningsCreditDefinitionCode || undefined,
      expiry: accountTypeOverride.earningsCreditDefinitionCodeExpiry || null,
    },
    {
      id: '2',
      name: 'Investable balance',
      field: 'investableBalanceDefinitionCode',
      override: !!accountTypeOverride.investableBalanceDefinitionCode,
      plan: accountTypeOverride.investableBalanceDefinitionCode || 'Default',
      planCode: accountTypeOverride.investableBalanceDefinitionCode || undefined,
      expiry: accountTypeOverride.investableBalanceDefinitionCodeExpiry || null,
    },
    {
      id: '3',
      name: 'Balance requirement',
      field: 'balanceRequirementDefinitionCode',
      override: !!accountTypeOverride.balanceRequirementDefinitionCode,
      plan: accountTypeOverride.balanceRequirementDefinitionCode || 'Default',
      planCode: accountTypeOverride.balanceRequirementDefinitionCode || undefined,
      expiry: accountTypeOverride.balanceRequirementDefinitionCodeExpiry || null,
    },
    {
      id: '4',
      name: 'Statement cycle',
      field: 'statementCyclePlanCode',
      override: !!accountTypeOverride.statementCyclePlanCode,
      plan: accountTypeOverride.statementCyclePlanCode || 'Default',
      planCode: accountTypeOverride.statementCyclePlanCode || undefined,
      expiry: accountTypeOverride.statementCyclePlanCodeExpiry || null,
    },
    {
      id: '5',
      name: 'Reserve requirement',
      field: 'reserveRequirementDefinitionCode',
      override: !!accountTypeOverride.reserveRequirementDefinitionCode,
      plan: accountTypeOverride.reserveRequirementDefinitionCode || 'Default',
      planCode: accountTypeOverride.reserveRequirementDefinitionCode || undefined,
      expiry: accountTypeOverride.reserveRequirementDefinitionCodeExpiry || null,
    },
    {
      id: '6',
      name: 'Interest',
      field: 'interestRequirementDefinitionCode',
      override: !!accountTypeOverride.interestRequirementDefinitionCode,
      plan: accountTypeOverride.interestRequirementDefinitionCode || 'Default',
      planCode: accountTypeOverride.interestRequirementDefinitionCode || undefined,
      expiry: accountTypeOverride.interestRequirementDefinitionCodeExpiry || null,
    },
  ] : []

  if (!account) {
    return <>404 could not load account.</>
  }

  if (isLoading) {
    return <div>Loading settlement and processing options...</div>
  }

  if (isError) {
    return <div>Error loading settlement and processing options.</div>
  }

  return (
    <div className='flex justify-between'>
      <InfoSection className='w-full !gap-1 md:w-3/4'>
        <div className='flex items-center justify-between'>
          <InfoSectionTitle>Settlements And Processing</InfoSectionTitle>
          <div className='flex gap-2'>
            <Button className='btn'>Export</Button>
            <Link
              href={`${routeTo(
                '/accounts/[effectiveMonth]/edit/[accountCode]/settlement-and-processing',
                routeParams,
              )}`}
            >
              <Button className='btn-primary text-white'>Edit</Button>
            </Link>
          </div>
        </div>
        <p className='text-zinc-400'>
          You can override the following settings for your composite account.
        </p>
        <ViewSettlementAccount
          account={account}
          chargeAccountCode={accountTypeOverride?.chargeAccountCode}
        />
        <SettlementOptions settlementOptions={settlementOptions}/>
        <ProcessingOptions processingOptions={processingOptions}/>
      </InfoSection>
    </div>
  )
}
